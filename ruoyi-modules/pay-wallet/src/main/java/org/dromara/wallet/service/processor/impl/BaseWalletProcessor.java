package org.dromara.wallet.service.processor.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.BaseConfigFacade;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.mapper.WalletCoinRecMapper;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.dromara.wallet.service.processor.WalletProcessResult;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Base链钱包处理器
 * 负责处理Base链的钱包余额记录
 *
 * <p>处理内容包括：</p>
 * <ul>
 *   <li>ETH原生代币余额</li>
 *   <li>ERC20合约代币余额</li>
 *   <li>租户信息处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaseWalletProcessor implements WalletChainProcessor {

    private final BaseConfigFacade baseConfigFacade;
    private final EvmHelper evmHelper;
    private final IWalletCoinRecService walletCoinRecService;

    @Override
    public ChainType getSupportedChainType() {
        return ChainType.BASE;
    }

    @Override
    public void processWallet(String walletAddress, String walletTenantId) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("BASE钱包地址不能为空");
            return;
        }

        log.debug("开始处理BASE钱包: 地址={}, 租户ID={}", walletAddress, walletTenantId);

        try {
            WalletProcessResult result = new WalletProcessResult(walletAddress, "BASE");

            // 处理ETH原生代币
            processEvmNativeToken(walletAddress, walletTenantId, "BASE", "ETH", "_ETH_NATIVE_", result);

            // 处理ERC20代币
            processEvmTokens(walletAddress, walletTenantId, "BASE", result);

            result.finish();
            if (result.hasFailures()) {
                log.warn(result.getSummary());
            } else {
                log.info(result.getSummary());
            }

            // 详细日志（debug级别）
            if (log.isDebugEnabled()) {
                log.debug(result.getDetailedSummary());
            }

        } catch (Exception e) {
            log.error("BASE钱包处理失败: 地址={}, 租户ID={}, 错误={}",
                walletAddress, walletTenantId, e.getMessage(), e);
            throw new RuntimeException("BASE钱包处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用EVM原生代币处理方法
     */
    private void processEvmNativeToken(String walletAddress, String walletTenantId, String chainType, String tokenSymbol, String tokenAddress, WalletProcessResult result) {
        try {
            // 使用统一入口查询原生代币余额
            BigDecimal nativeBalance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, baseConfigFacade);
            if (nativeBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType(chainType);
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress(tokenAddress);
                balanceRecord.setTokenSymbol(tokenSymbol);
                balanceRecord.setBalance(nativeBalance);
                balanceRecord.setDecimals(18); // 原生代币默认18位小数
                balanceRecord.setCreateBy(1111L);
                balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                walletCoinRecService.insertByBo(balanceRecord);
                result.addSuccess(tokenSymbol, nativeBalance);
            }
        } catch (Exception e) {
            result.addFailure(tokenSymbol);
            log.debug("处理{}原生代币失败: {}, error: {}", chainType, walletAddress, e.getMessage());
        }
    }

    /**
     * 通用EVM代币处理方法
     */
    private void processEvmTokens(String walletAddress, String walletTenantId, String chainType, WalletProcessResult result) {
        for (String tokenSymbol : baseConfigFacade.getEnabledTokenSymbols()) {
            try {
                String contractAddress = baseConfigFacade.getContractAddress(tokenSymbol);
                if (contractAddress == null || contractAddress.trim().isEmpty()) {
                    log.debug("{}代币{}缺少合约地址，跳过处理", chainType, tokenSymbol);
                    result.addFailure(tokenSymbol);
                    continue;
                }

                // 使用统一入口查询代币余额
                BigDecimal balance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, baseConfigFacade);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType(chainType);
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(contractAddress);
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(baseConfigFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);
                    balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                    walletCoinRecService.insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理{}代币失败: {} {}, error: {}", chainType, walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }
}
