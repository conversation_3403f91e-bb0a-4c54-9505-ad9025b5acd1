package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.domain.WalletCoinRec;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;
import org.dromara.wallet.mapper.WalletCoinRecMapper;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.dromara.wallet.wallet.monitor.event.BalanceChangeEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 多链用户钱包代币余额记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Service
public class WalletCoinRecServiceImpl implements IWalletCoinRecService {

    private final WalletCoinRecMapper baseMapper;

    // 策略模式：钱包链处理器映射
    private final Map<ChainType, WalletChainProcessor> chainProcessors;

    /**
     * 构造函数 - 初始化策略映射
     */
    public WalletCoinRecServiceImpl(
        WalletCoinRecMapper baseMapper,
        @Lazy List<WalletChainProcessor> processors) {

        this.baseMapper = baseMapper;

        // 初始化策略映射
        this.chainProcessors = new HashMap<>();
        for (WalletChainProcessor processor : processors) {
            chainProcessors.put(processor.getSupportedChainType(), processor);
            log.debug("注册钱包处理器: {} -> {}",
                processor.getSupportedChainType(), processor.getProcessorName());
        }

        log.info("钱包处理器策略映射初始化完成，支持的链: {}", chainProcessors.keySet());
    }

    /**
     * 处理钱包监控事件
     */
    @EventListener
    @Async
    public void handleBalanceChangeEvent(BalanceChangeEvent event) {
        insertByFlatChain(event.getChainType(), event.getAddress(),event.getWalletTenantId());
    }

    /**
     * 根据扁平化链类型插入代币余额记录
     * 推荐使用此方法，更符合扁平化配置理念
     * 使用策略模式重构，统一处理逻辑
     */
    @Override
    public void insertByFlatChain(ChainType chainType, String address, String walletTenantId) {
        log.info("开始处理钱包余额记录: 地址={}, 链类型={}, 租户ID={}", address, chainType, walletTenantId);

        WalletChainProcessor processor = chainProcessors.get(chainType);
        if (processor != null) {
            try {
                processor.processWallet(address, walletTenantId);
                log.debug("钱包处理完成: 地址={}, 链类型={}", address, chainType);
            } catch (Exception e) {
                log.error("钱包处理失败: 地址={}, 链类型={}, 错误={}", address, chainType, e.getMessage(), e);
                throw e; // 重新抛出异常，保持原有的错误处理逻辑
            }
        } else {
            log.warn("不支持的扁平化链类型: {}", chainType);
            throw new IllegalArgumentException("不支持的链类型: " + chainType);
        }
    }



    /**
     * 查询多链用户钱包代币余额记录
     *
     * @param id 主键
     * @return 多链用户钱包代币余额记录
     */
    @Override
    public WalletCoinRecVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据钱包地址查询代币余额记录
     *
     * @param walletAddress 钱包地址
     * @return 多链用户钱包代币余额记录列表
     */
    @Override
    public List<WalletCoinRecVo> queryByWalletAddress(String walletAddress) {
        LambdaQueryWrapper<WalletCoinRec> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(walletAddress), WalletCoinRec::getWalletAddress, walletAddress);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询每个钱包地址和代币组合的最新记录（专门优化的方法）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 最新记录分页列表
     */
    @Override
    public TableDataInfo<WalletCoinRecVo> queryLatestRecordsPage(WalletCoinRecBo bo, PageQuery pageQuery) {
        Page<WalletCoinRecVo> page = pageQuery.build();

        // 构建查询条件，复用已有的buildQueryWrapper方法
        LambdaQueryWrapper<WalletCoinRec> queryWrapper = buildQueryWrapper(bo);

        // 使用MyBatis-Plus标准方式调用
        IPage<WalletCoinRecVo> result = baseMapper.selectLatestRecordsPage(page, queryWrapper);

        // todo 扩展归集钱包信息
        result.getRecords().forEach(r -> {
            String tenantId = r.getTenantId();
//            MetaMainAddress mainAddress = tronConfigFacade.getWalletConfig().getMainAddress();

        });

        return TableDataInfo.build(result);
    }

    /**
     * 分页查询多链用户钱包代币余额记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 多链用户钱包代币余额记录分页列表
     */
    @Override
    public TableDataInfo<WalletCoinRecVo> queryPageList(WalletCoinRecBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WalletCoinRec> lqw = buildQueryWrapper(bo);

        // 如果需要只查询最新记录，使用专门优化的方法
        if (Boolean.TRUE.equals(bo.getOnlyLatest())) {
            log.info("检测到最新记录查询，建议使用 queryLatestRecordsPage() 方法以获得更好的性能");
            return queryLatestRecordsPage(bo, pageQuery);
        } else {
            // 正常分页查询
            Page<WalletCoinRecVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }
    }

    /**
     * 查询符合条件的多链用户钱包代币余额记录列表
     *
     * @param bo 查询条件
     * @return 多链用户钱包代币余额记录列表
     */
    @Override
    public List<WalletCoinRecVo> queryList(WalletCoinRecBo bo) {
        LambdaQueryWrapper<WalletCoinRec> lqw = buildQueryWrapper(bo);

        // 如果需要只查询最新记录，使用专门优化的方法
        if (Boolean.TRUE.equals(bo.getOnlyLatest())) {
            log.info("检测到最新记录查询，建议使用 queryLatestRecordsPage() 方法以获得更好的性能");
            // 对于列表查询中的最新记录需求，创建大分页查询获取所有结果
            PageQuery largePageQuery = new PageQuery();
            largePageQuery.setPageNum(1);
            largePageQuery.setPageSize(10000); // 使用大分页获取结果
            return queryLatestRecordsPage(bo, largePageQuery).getRows();
        } else {
            return baseMapper.selectVoList(lqw);
        }
    }

    /**
     * 构建查询条件Wrapper，更多使用 MyBatis-Plus Lambda 表达式
     */
    private LambdaQueryWrapper<WalletCoinRec> buildQueryWrapper(WalletCoinRecBo bo) {
        Map<String, Object> params = bo.getParams();

        return Wrappers.<WalletCoinRec>lambdaQuery()
            // 基础查询条件
            .eq(bo.getId() != null, WalletCoinRec::getId, bo.getId())
            .like(StringUtils.isNotBlank(bo.getWalletAddress()), WalletCoinRec::getWalletAddress, bo.getWalletAddress())
            .like(StringUtils.isNotBlank(bo.getTokenAddress()), WalletCoinRec::getTokenAddress, bo.getTokenAddress())
            .eq(StringUtils.isNotBlank(bo.getTokenSymbol()), WalletCoinRec::getTokenSymbol, bo.getTokenSymbol())
            .eq(bo.getDecimals() != null, WalletCoinRec::getDecimals, bo.getDecimals())
            .eq(StringUtils.isNotBlank(bo.getChainType()), WalletCoinRec::getChainType, bo.getChainType());

        // 注意：balance > 1 的条件已在SQL中固定设置，不需要在这里添加动态条件
    }

    /**
     * 新增多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WalletCoinRecBo bo) {
        WalletCoinRec add = MapstructUtils.convert(bo, WalletCoinRec.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WalletCoinRecBo bo) {
        WalletCoinRec update = MapstructUtils.convert(bo, WalletCoinRec.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除多链用户钱包代币余额记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 进行业务校验
        return baseMapper.deleteByIds(ids) > 0;
    }

}
