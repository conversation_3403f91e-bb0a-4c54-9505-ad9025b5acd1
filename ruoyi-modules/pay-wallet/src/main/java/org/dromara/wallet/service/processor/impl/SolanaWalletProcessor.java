package org.dromara.wallet.service.processor.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.springframework.stereotype.Component;

/**
 * Solana链钱包处理器
 * 负责处理Solana链的钱包余额记录
 *
 * <p>处理内容包括：</p>
 * <ul>
 *   <li>SOL原生代币余额</li>
 *   <li>SPL合约代币余额</li>
 *   <li>租户信息处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaWalletProcessor implements WalletChainProcessor {

    private final SolanaConfigFacade solanaConfigFacade;
    private final SolanaHelper solanaHelper;
    private final IWalletCoinRecService walletCoinRecService;

    @Override
    public ChainType getSupportedChainType() {
        return ChainType.SOLANA;
    }

    @Override
    public void processWallet(String walletAddress, String walletTenantId) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("Solana钱包地址不能为空");
            return;
        }

        log.debug("开始处理Solana钱包: 地址={}, 租户ID={}", walletAddress, walletTenantId);

        try {
            // 使用策略模式调用统一入口
            walletCoinRecService.insertByFlatChain(ChainType.SOLANA, walletAddress, walletTenantId);

            log.debug("Solana钱包处理完成: 地址={}", walletAddress);

        } catch (Exception e) {
            log.error("Solana钱包处理失败: 地址={}, 租户ID={}, 错误={}",
                walletAddress, walletTenantId, e.getMessage(), e);
            throw new RuntimeException("Solana钱包处理失败: " + e.getMessage(), e);
        }
    }
}
